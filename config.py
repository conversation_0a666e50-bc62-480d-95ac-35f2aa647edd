"""
富途牛牛API监控配置文件
可以在这里修改各种参数
"""

# 股票配置
STOCK_CONFIG = {
    "default_stock_id": "201335",  # 默认股票ID (特斯拉)
    "interval_minutes": 1,         # 请求间隔（分钟）
}

# 其他常用股票ID（可以参考）
COMMON_STOCKS = {
    "TSLA": "201335",    # 特斯拉
    "AAPL": "200002",    # 苹果（示例，需要确认实际ID）
    "GOOGL": "200004",   # 谷歌（示例，需要确认实际ID）
    "MSFT": "200003",    # 微软（示例，需要确认实际ID）
}

# 认证相关配置（这些可能需要定期更新）
AUTH_CONFIG = {
    "csrf_token": "kS5F1ttCe3e2y8-deNuYu8b_",
    "quote_token": "011443a811",
    "device_id": "1748620665116103",
    "uid": "21578506",
}

# 请求配置
REQUEST_CONFIG = {
    "timeout": 30,           # 请求超时时间（秒）
    "max_retries": 3,        # 最大重试次数
    "retry_delay": 5,        # 重试延迟（秒）
}

# 输出配置
OUTPUT_CONFIG = {
    "save_to_file": False,           # 是否保存到文件
    "output_file": "api_log.txt",    # 输出文件名
    "show_full_response": True,      # 是否显示完整响应
    "show_timestamp": True,          # 是否显示时间戳
}
