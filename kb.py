from pynput import keyboard
import threading
import time
import json
import os
from queue import Queue
from dataclasses import dataclass
from typing import Dict, Union, Any

# 定义大键盘上的按键的虚拟键码（macOS/Darwin）
MAIN_2_VK = 19  # 大键盘上的2
MAIN_3_VK = 20  # 大键盘上的3
MAIN_4_VK = 21  # 大键盘上的4

# 定义反引号键的虚拟键码（根据需要调整）
BACKTICK_VK = 50  # 反引号键 `

@dataclass
class KeyConfig:
    key: Union[str, Any]  # 可以是字符串或键盘按键对象
    interval: float
    initial_delay: float
    last_trigger: float = 0
    started: bool = False

class D3Helper:
    def __init__(self):
        self.running = False
        self.command_queue = Queue()
        # 使用大键盘上的按键（而非数字键盘）- 使用字符键
        self.key_configs = {
            '2': KeyConfig('2', interval=1.0, initial_delay=0.0),  # 使用字符 '2'，更可靠
            '3': KeyConfig('3', interval=1.0, initial_delay=20.0),   # 使用字符 '3'，更可靠
            '4': KeyConfig('4', interval=1.0, initial_delay=0.0)    # 使用字符 '4'，更可靠
        }
        self.keyboard_controller = keyboard.Controller()
        self.load_config()

        # 启动执行线程
        self.executor = threading.Thread(target=self._execute_loop, daemon=True)
        self.executor.start()

        # 启动调度线程
        self.scheduler = threading.Thread(target=self._schedule_loop, daemon=True)
        self.scheduler.start()

    def load_config(self):
        try:
            if os.path.exists('d3_config.json'):
                with open('d3_config.json', 'r') as f:
                    config = json.load(f)
                    for key, settings in config.items():
                        if key in self.key_configs:
                            if isinstance(settings, dict):
                                self.key_configs[key].interval = float(settings.get('interval', 1.0))
                                self.key_configs[key].initial_delay = float(settings.get('initial_delay', 0.0))
                            else:
                                # 兼容旧版本配置文件
                                self.key_configs[key].interval = float(settings)
                    # 注意：配置文件只保存间隔和延迟，不保存按键对象
        except Exception as e:
            print(f"加载配置文件出错: {e}")
            print("使用默认配置")

    def save_config(self):
        try:
            config = {k: {
                'interval': v.interval,
                'initial_delay': v.initial_delay
            } for k, v in self.key_configs.items()}
            with open('d3_config.json', 'w') as f:
                json.dump(config, f)
        except Exception as e:
            print(f"保存配置文件出错: {e}")

    def set_interval(self, key: str, interval: float):
        if key in self.key_configs:
            self.key_configs[key].interval = interval
            self.save_config()

    def set_initial_delay(self, key: str, delay: float):
        if key in self.key_configs:
            self.key_configs[key].initial_delay = delay
            self.save_config()

    def toggle(self):
        self.running = not self.running
        if self.running:
            start_time = time.time()
            for config in self.key_configs.values():
                config.started = False
                config.last_trigger = start_time
        print(f"脚本已{'启动' if self.running else '停止'}")

    def _execute_key(self, key: Union[str, Any]):
        for _ in range(3):
            self.keyboard_controller.press(key)
            time.sleep(0.01)
            self.keyboard_controller.release(key)
            time.sleep(0.01)

    def _execute_loop(self):
        while True:
            try:
                key = self.command_queue.get()
                if self.running:
                    self._execute_key(key)
            except Exception as e:
                print(f"执行按键出错: {e}")

    def _schedule_loop(self):
        while True:
            try:
                if self.running:
                    current_time = time.time()
                    for key_config in self.key_configs.values():
                        if not key_config.started:
                            if current_time - key_config.last_trigger >= key_config.initial_delay:
                                key_config.started = True
                                key_config.last_trigger = current_time
                                self.command_queue.put(key_config.key)
                        else:
                            if current_time - key_config.last_trigger >= key_config.interval:
                                self.command_queue.put(key_config.key)
                                key_config.last_trigger = current_time
                time.sleep(0.01)
            except Exception as e:
                print(f"调度出错: {e}")

def main():
    try:
        helper = D3Helper()

        def on_press(key):
            try:
                # 调试信息：打印按键信息
                # print(f"按下按键: {key}, 类型: {type(key)}")
                # if hasattr(key, 'vk'):
                #     print(f"虚拟键码(vk): {key.vk}")
                # if hasattr(key, 'char'):
                #     print(f"字符(char): {key.char}")

                # 在 macOS 上，检查按键的虚拟键码和字符
                if hasattr(key, 'vk'):
                    # 检查是否是反引号键
                    if hasattr(key, 'char') and key.char == '`':
                        helper.toggle()
                    # 检查是否是大键盘上的2、3、4键
                    elif key.vk == MAIN_2_VK and hasattr(key, 'char') and key.char == '2':
                        #print("检测到按下大键盘上的2键")
                        if helper.running:
                            helper.command_queue.put(helper.key_configs['2'].key)
                    elif key.vk == MAIN_3_VK and hasattr(key, 'char') and key.char == '3':
                        #print("检测到按下大键盘上的3键")
                        if helper.running:
                            helper.command_queue.put(helper.key_configs['3'].key)
                    elif key.vk == MAIN_4_VK and hasattr(key, 'char') and key.char == '4':
                        #print("检测到按下大键盘上的4键")
                        if helper.running:
                            helper.command_queue.put(helper.key_configs['4'].key)
            except AttributeError as e:
                # 处理特殊键（如F1、Esc等）
                print(f"按键处理错误: {e}")
                pass

        listener = keyboard.Listener(on_press=on_press)
        listener.start()

        print("\n暗黑3技能助手已启动")
        print("使用说明：")
        print("1. 按 ` 键切换脚本开关")
        print("2. 当前设置（使用大键盘上的数字键，非数字小键盘）：")
        for key, config in helper.key_configs.items():
            print(f"   键位 {key}: 间隔 {config.interval}秒, 初始延时 {config.initial_delay}秒")
        print("3. 输入'set 键位 间隔'来修改间隔时间")
        print("   例如: set 2 1.5")
        print("4. 输入'delay 键位 延时'来修改初始延时")
        print("   例如: delay 2 20")
        print("5. 输入'exit'退出程序\n")

        while True:
            try:
                cmd = input().strip()
                if cmd == 'exit':
                    break
                elif cmd.startswith('set '):
                    try:
                        _, key, interval = cmd.split()
                        helper.set_interval(key, float(interval))
                        print(f"已设置键位 {key} 的间隔为 {interval} 秒")
                    except:
                        print("输入格式错误，请使用'set 键位 间隔'的格式")
                elif cmd.startswith('delay '):
                    try:
                        _, key, delay = cmd.split()
                        helper.set_initial_delay(key, float(delay))
                        print(f"已设置键位 {key} 的初始延时为 {delay} 秒")
                    except:
                        print("输入格式错误，请使用'delay 键位 延时'的格式")
            except KeyboardInterrupt:
                continue

        listener.stop()
    except Exception as e:
        print(f"程序出错: {e}")

if __name__ == "__main__":
    main()