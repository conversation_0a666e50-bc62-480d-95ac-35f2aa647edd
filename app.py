from pynput import keyboard
from pynput.keyboard import Controller as KeyController
import threading
import time

# 全局控制变量
running = False
key_controller = KeyController()

class GameTask:
    def __init__(self, delay, interval, key_vk, press_times, press_interval):
        self.delay = delay
        self.interval = interval
        self.key_vk = key_vk
        self.press_times = press_times
        self.press_interval = press_interval
        self.thread = threading.Thread(target=self._monitor, daemon=True)
        self.thread.start()

    def _monitor(self):
        while True:
            while not running:
                time.sleep(0.1)
            
            time.sleep(self.delay)
            
            while running:
                threading.Thread(target=self._execute).start()
                
                start_time = time.time()
                while running and (time.time() - start_time) < self.interval:
                    time.sleep(0.1)

    def _execute(self):
        # 使用正确的虚拟键码发送按键
        key = keyboard.KeyCode.from_vk(self.key_vk)
        for _ in range(self.press_times):
            key_controller.press(key)
            key_controller.release(key)
            time.sleep(self.press_interval)

def on_press(key):
    global running
    try:
        # 调试日志保留用于验证
        # print(f"[DEBUG] 按键检测: {key} | char={key.char} | vk={key.vk}")
        
        # 根据实际检测值调整`键检测
        if key.char == '`' or key.vk == 0xC0:
            running = not running
            status = "启动" if running else "暂停"
            print(f"脚本状态：{status}")
    except AttributeError:
        pass

# 修正后的任务配置（使用实际检测到的vk值）
tasks = [
    GameTask(delay=1, interval=1, key_vk=19, press_times=5, press_interval=0.5),  # 对应2键
    # GameTask(delay=12, interval=5, key_vk=20, press_times=3, press_interval=5),  # 对应3键
    # GameTask(delay=10, interval=10, key_vk=21, press_times=2, press_interval=5)   # 对应4键
]

# 启动键盘监听
with keyboard.Listener(on_press=on_press) as listener:
    print("按键辅助脚本已启动（修正版）")
    print("按 ` 键切换运行/暂停状态")
    listener.join()