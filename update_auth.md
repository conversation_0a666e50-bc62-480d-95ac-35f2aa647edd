# 更新认证信息指南

当API返回"Params Error"或认证相关错误时，需要更新 `config.py` 中的认证信息。

## 获取最新认证信息的步骤

### 1. 打开浏览器开发者工具

1. 打开Chrome或其他浏览器
2. 按 `F12` 或右键选择"检查"打开开发者工具
3. 切换到 "Network" (网络) 标签页

### 2. 访问富途牛牛网站

1. 访问 https://www.futunn.com
2. 登录你的账户
3. 导航到任意股票页面，例如：https://www.futunn.com/stock/TSLA-US

### 3. 找到API请求

1. 在Network标签页中，过滤请求类型为 "XHR" 或 "Fetch"
2. 刷新页面或进行一些操作触发API请求
3. 寻找类似这样的请求：
   ```
   get-real-cash-trend?stockId=201335&_=1748622372757
   ```

### 4. 复制认证信息

点击找到的API请求，在右侧面板中：

#### 从Request Headers复制：
- `futu-x-csrf-token`: 复制到 `AUTH_CONFIG["csrf_token"]`
- `quote-token`: 复制到 `AUTH_CONFIG["quote_token"]`

#### 从Cookies复制：
- `csrfToken`: 复制到 `AUTH_CONFIG["csrf_token"]` (通常与futu-x-csrf-token相同)
- `device_id`: 复制到 `AUTH_CONFIG["device_id"]`
- `uid`: 复制到 `AUTH_CONFIG["uid"]`

### 5. 更新config.py

将获取的信息更新到 `config.py` 文件中：

```python
AUTH_CONFIG = {
    "csrf_token": "你的新csrf_token",
    "quote_token": "你的新quote_token", 
    "device_id": "你的新device_id",
    "uid": "你的新uid",
}
```

### 6. 测试更新

运行测试脚本验证更新是否成功：

```bash
python test_api.py
```

如果看到 "✅ API请求成功！" 说明认证信息更新成功。

## 示例：完整的curl请求

你提供的curl请求中包含了所有需要的信息：

```bash
curl 'https://www.futunn.com/quote-api/quote-v2/get-real-cash-trend?stockId=201335&_=1748621700611' \
  -H 'futu-x-csrf-token: kS5F1ttCe3e2y8-deNuYu8b_' \
  -H 'quote-token: 011443a811' \
  -b 'csrfToken=kS5F1ttCe3e2y8-deNuYu8b_; device_id=1748620665116103; uid=21578506; ...'
```

从中提取：
- `csrf_token`: `kS5F1ttCe3e2y8-deNuYu8b_`
- `quote_token`: `011443a811`
- `device_id`: `1748620665116103`
- `uid`: `21578506`

## 注意事项

1. **认证信息有效期**：这些认证信息通常有时效性，可能需要定期更新
2. **安全性**：不要在公共场所或不安全的网络环境下获取认证信息
3. **备份**：建议保存一份有效的认证信息作为备份

## 常见问题

### Q: 为什么会出现"Params Error"？
A: 通常是因为：
- 认证信息过期
- 股票ID格式错误
- 请求头缺少必要信息

### Q: 多久需要更新一次认证信息？
A: 这取决于富途牛牛的会话管理策略，通常在几小时到几天之间。

### Q: 可以自动更新认证信息吗？
A: 理论上可以，但需要模拟登录过程，比较复杂且可能违反服务条款。
