#!/usr/bin/env python3
"""
测试富途牛牛API的简单脚本
用于验证API请求是否正常工作
"""

from futunn_api_monitor import FutunnAPIMonitor

def test_single_request():
    """测试单次API请求"""
    print("测试富途牛牛API单次请求...")
    
    monitor = FutunnAPIMonitor("201335")  # 特斯拉股票ID
    
    print("发起请求...")
    response = monitor.make_request()
    
    if response:
        print(f"请求成功！状态码: {response.status_code}")
        print(f"请求URL: {response.url}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print("响应数据:")
                print(f"- 代码: {data.get('code', 'N/A')}")
                print(f"- 消息: {data.get('message', 'N/A')}")
                print(f"- 数据长度: {len(data.get('data', []))}")
                
                if data.get('code') == 0:
                    print("✅ API请求成功！")
                else:
                    print(f"⚠️  API返回错误: {data.get('message')}")
                    print("可能的原因:")
                    print("1. 认证信息过期，需要更新config.py中的认证参数")
                    print("2. 股票ID无效")
                    print("3. 请求参数有误")
                    
            except Exception as e:
                print(f"❌ 解析响应数据失败: {e}")
        else:
            print(f"❌ HTTP请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
    else:
        print("❌ 请求失败，无响应")

if __name__ == "__main__":
    test_single_request()
