#!/bin/bash

# 富途牛牛API监控脚本启动器

# 激活虚拟环境
source venv/bin/activate

echo "富途牛牛API监控脚本"
echo "===================="
echo ""

# 检查参数
if [ "$1" = "test" ]; then
    echo "运行测试模式..."
    python test_api.py
elif [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    echo "使用方法:"
    echo "  ./run.sh                    # 使用默认配置运行监控"
    echo "  ./run.sh test              # 运行单次测试"
    echo "  ./run.sh TSLA              # 监控特斯拉股票"
    echo "  ./run.sh 201335 2          # 监控股票ID 201335，每2分钟请求一次"
    echo "  ./run.sh help              # 显示此帮助信息"
    echo ""
    echo "预定义股票:"
    echo "  TSLA   - 特斯拉 (201335)"
    echo "  AAPL   - 苹果 (需要确认ID)"
    echo "  GOOGL  - 谷歌 (需要确认ID)"
    echo "  MSFT   - 微软 (需要确认ID)"
    echo ""
    echo "注意: 如果遇到认证错误，请参考 update_auth.md 更新认证信息"
else
    echo "启动监控..."
    python futunn_api_monitor.py "$@"
fi
