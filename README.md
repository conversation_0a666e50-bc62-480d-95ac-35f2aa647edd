# 富途牛牛API监控脚本

这是一个用于监控富途牛牛股票API的Python脚本，可以每分钟自动请求接口并打印返回结果。

## 功能特点

- 🔄 自动每分钟请求一次API
- 📊 实时显示股票数据
- ⚙️ 可配置的参数设置
- 🎯 支持多种股票ID
- 🛡️ 错误处理和重试机制
- 📝 详细的日志输出

## 文件说明

- `futunn_api_monitor.py` - 主监控脚本
- `config.py` - 配置文件，包含所有可调整的参数
- `README.md` - 使用说明文档

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 基本使用

```bash
# 使用默认配置（特斯拉股票，每分钟请求一次）
python futunn_api_monitor.py

# 指定股票ID
python futunn_api_monitor.py 201335

# 指定股票ID和请求间隔（分钟）
python futunn_api_monitor.py 201335 2

# 使用预定义的股票名称
python futunn_api_monitor.py TSLA
```

### 配置参数

在 `config.py` 文件中可以修改以下参数：

#### 股票配置
```python
STOCK_CONFIG = {
    "default_stock_id": "201335",  # 默认股票ID
    "interval_minutes": 1,         # 请求间隔（分钟）
}
```

#### 认证配置
```python
AUTH_CONFIG = {
    "csrf_token": "your_csrf_token",    # CSRF令牌
    "quote_token": "your_quote_token",  # 报价令牌
    "device_id": "your_device_id",      # 设备ID
    "uid": "your_uid",                  # 用户ID
}
```

#### 请求配置
```python
REQUEST_CONFIG = {
    "timeout": 30,           # 请求超时时间（秒）
    "max_retries": 3,        # 最大重试次数
    "retry_delay": 5,        # 重试延迟（秒）
}
```

## 动态参数说明

脚本会自动处理以下动态参数：

1. **时间戳参数 (`_`)**：每次请求时自动生成当前时间戳（毫秒级），防止缓存
2. **股票ID (`stockId`)**：可通过命令行参数或配置文件指定
3. **认证令牌**：从配置文件读取，包括CSRF令牌、报价令牌等

## 输出格式

脚本会显示以下信息：

```
============================================================
请求时间: 2024-01-30 15:30:45
状态码: 200
股票ID: 201335
请求URL: https://www.futunn.com/quote-api/quote-v2/get-real-cash-trend?stockId=201335&_=1748621700611
============================================================
响应数据:
{
  "code": 0,
  "message": "success",
  "data": {
    // API返回的股票数据
  }
}
============================================================
```

## 注意事项

### 重要提醒

1. **认证信息更新**：`config.py` 中的认证信息（如csrf_token、quote_token等）可能会过期，需要定期更新
2. **请求频率**：建议不要设置过高的请求频率，避免被服务器限制
3. **网络连接**：确保网络连接稳定，脚本会自动处理网络错误

### 获取认证信息

要获取最新的认证信息，可以：

1. 打开浏览器开发者工具
2. 访问富途牛牛网站
3. 在Network标签页中找到相关API请求
4. 复制请求头中的认证信息到 `config.py`

### 常见股票ID

```python
COMMON_STOCKS = {
    "TSLA": "201335",    # 特斯拉
    "AAPL": "200002",    # 苹果（示例）
    "GOOGL": "200004",   # 谷歌（示例）
    "MSFT": "200003",    # 微软（示例）
}
```

## 停止监控

按 `Ctrl+C` 可以停止监控脚本。

## 故障排除

### 常见问题

1. **401/403错误**：认证信息过期，需要更新 `config.py` 中的认证参数
2. **网络超时**：检查网络连接，或增加 `REQUEST_CONFIG["timeout"]` 值
3. **股票ID无效**：确认股票ID是否正确

### 调试模式

如果遇到问题，可以在脚本中添加更多调试信息，或者检查API返回的错误消息。

## 许可证

本项目仅供学习和研究使用，请遵守富途牛牛的服务条款。
